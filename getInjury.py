import requests
from bs4 import BeautifulSoup
from supabase import create_client, Client

# Configuration Supabase
SUPABASE_URL = "https://ezjthfrjbqwpxrksijml.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV6anRoZnJqYnF3cHhya3Npam1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIxMzAwNzksImV4cCI6MjA0NzcwNjA3OX0.e3HGapMU19JmJjI52yqAsrNbNOVIxYGbdA5xNNF7RrA"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def fetch_injuries():
    url = "https://www.hockey-reference.com/friv/injuries.cgi"
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    table = soup.find('table', {'id': 'injuries'})
    if not table:
        print("Table des blessures introuvable")
        return {"error": "Table des blessures introuvable"}

    injuries_data = []
    rows = table.find_all('tr')
    for row in rows[1:]:
        header_cell = row.find('th')
        cells = [header_cell] + row.find_all('td') if header_cell else row.find_all('td')
        if len(cells) != 5:
            continue
        injury = {
            "Player": cells[0].get_text(strip=True),
            "Team": cells[1].get_text(strip=True),
            "Date": cells[2].get_text(strip=True),
            "Status": cells[3].get_text(strip=True),
            "Description": cells[4].get_text(strip=True)
        }
        injuries_data.append(injury)
    return injuries_data

if __name__ == "__main__":
    try:
        # 🟦 Copier les données existantes de injury_player vers injury_player_temp
        existing_data = supabase.table("injury_player").select("*").execute()
        if existing_data.data:
            for row in existing_data.data:
                row.pop("injury_id", None)
                supabase.table("injury_player_temp").insert(row).execute()
            print("✅ Copie vers injury_player_temp terminée.")
        else:
            print("Aucune donnée à copier.")

        # 🟥 Supprimer les données actuelles
        supabase.table("injury_player").delete().neq("injury_id", 0).execute()
        print("🧹 injury_player vidé.")

        # 🟩 Insérer les nouvelles blessures
        injuries = fetch_injuries()
        if "error" in injuries:
            print("❌ Erreur récupération blessures")
        else:
            for injury in injuries:
                player_response = supabase.table("players").select("playerid").eq("skaterfullname", injury['Player']).execute()
                if not player_response.data:
                    print(f"Joueur introuvable: {injury['Player']}")
                    continue
                playerid = player_response.data[0]["playerid"]
                data = {
                    "team": injury['Team'],
                    "strinjurydate": injury['Date'],
                    "status": injury['Status'],
                    "decription": injury['Description'],
                    "playerfullname": injury['Player'],
                    "playerid": playerid
                }
                supabase.table("injury_player").insert(data).execute()
            print("✅ Nouvelles blessures insérées.")

        # 🟨 Comparaison pour joueurs rétablis
        temp_players = supabase.table("injury_player_temp").select("*").execute()
        current_players = supabase.table("injury_player").select("playerid").execute()

        temp_ids = {item['playerid'] for item in temp_players.data}
        current_ids = {item['playerid'] for item in current_players.data}
        recovered_ids = temp_ids - current_ids

        # if recovered_ids:
        #     print(f"🩹 Joueurs rétablis : {recovered_ids}")
        #     for pid in recovered_ids:
        #         record = next((p for p in temp_players.data if p['playerid'] == pid), None)
        #         if record:
        #             record.pop("injury_id", None)
        #             supabase.table("injury_return").insert(record).execute()
        #             print(f"➡️ Joueur inséré dans injury_return : {record['playerfullname']}")
        
        if recovered_ids:
            print(f"🩹 Joueurs rétablis : {recovered_ids}")
            for pid in recovered_ids:
                record = next((p for p in temp_players.data if p['playerid'] == pid), None)
                if record:
                    # Supprimer toutes les colonnes non présentes dans la table injury_return
                    for key in list(record.keys()):
                        if key not in ["playerid", "team", "strinjurydate", "status", "decription", "playerfullname"]:
                            record.pop(key)
                    supabase.table("injury_return").insert(record).execute()
                    print(f"➡️ Joueur inséré dans injury_return : {record['playerfullname']}")

        else:
            print("✅ Aucun joueur récupéré trouvé.")

        # ✅ Vider la table temporaire
        supabase.table("injury_player_temp").delete().neq("injurytemp_id", 0).execute()
        print("🗑️ injury_player_temp vidée.")

    except Exception as e:
        print(f"❌ Erreur globale : {e}")
